"use client";

import { useState } from "react";
import { AppCheckbox } from "../components/app-checkbox";
import { H3, Body2 } from "../components/app-typography";
import { FaInfo } from "react-icons/fa";

export default function CheckboxDemo() {
  const [basicChecked, setBasicChecked] = useState(false);
  const [labelChecked, setLabelChecked] = useState(true);
  const [disabledChecked, setDisabledChecked] = useState(false);
  const [errorChecked, setErrorChecked] = useState(false);
  const [tooltipChecked, setTooltipChecked] = useState(false);
  const [iconChecked, setIconChecked] = useState(false);

  return (
    <div className="min-h-screen bg-back-neutral-primary p-8">
      <div className="mx-auto max-w-4xl space-y-8">
        <div className="text-center">
          <H3 className="mb-4">Custom Checkbox Component Demo</H3>
          <Body2 color="secondary">
            Showcasing the redesigned AppCheckbox with hidden native checkbox and custom design
          </Body2>
        </div>

        <div className="grid gap-8 md:grid-cols-2">
          {/* Basic Checkbox */}
          <div className="rounded-xl border border-stroke-neutral-decorative bg-back-neutral-secondary p-6">
            <H3 className="mb-4">Basic Checkbox</H3>
            <div className="space-y-4">
              <AppCheckbox
                checked={basicChecked}
                onChange={(e) => setBasicChecked(e.target.checked)}
              />
              <Body2 color="secondary">
                State: {basicChecked ? "Checked" : "Unchecked"}
              </Body2>
            </div>
          </div>

          {/* Checkbox with Label */}
          <div className="rounded-xl border border-stroke-neutral-decorative bg-back-neutral-secondary p-6">
            <H3 className="mb-4">Checkbox with Label</H3>
            <div className="space-y-4">
              <AppCheckbox
                checked={labelChecked}
                onChange={(e) => setLabelChecked(e.target.checked)}
                label="Accept terms and conditions"
              />
              <Body2 color="secondary">
                State: {labelChecked ? "Accepted" : "Not accepted"}
              </Body2>
            </div>
          </div>

          {/* Disabled States */}
          <div className="rounded-xl border border-stroke-neutral-decorative bg-back-neutral-secondary p-6">
            <H3 className="mb-4">Disabled States</H3>
            <div className="space-y-4">
              <AppCheckbox
                checked={false}
                disabled
                label="Disabled unchecked"
              />
              <AppCheckbox
                checked={true}
                disabled
                label="Disabled checked"
              />
            </div>
          </div>

          {/* Error State */}
          <div className="rounded-xl border border-stroke-neutral-decorative bg-back-neutral-secondary p-6">
            <H3 className="mb-4">Error State</H3>
            <div className="space-y-4">
              <AppCheckbox
                checked={errorChecked}
                onChange={(e) => setErrorChecked(e.target.checked)}
                label="Required field"
                error="This field is required"
              />
            </div>
          </div>

          {/* Checkbox with Tooltip */}
          <div className="rounded-xl border border-stroke-neutral-decorative bg-back-neutral-secondary p-6">
            <H3 className="mb-4">Checkbox with Tooltip</H3>
            <div className="space-y-4">
              <AppCheckbox
                checked={tooltipChecked}
                onChange={(e) => setTooltipChecked(e.target.checked)}
                label="Enable notifications"
                tooltip="You will receive email notifications about important updates"
              />
            </div>
          </div>

          {/* Checkbox with Icon */}
          <div className="rounded-xl border border-stroke-neutral-decorative bg-back-neutral-secondary p-6">
            <H3 className="mb-4">Checkbox with Icon</H3>
            <div className="space-y-4">
              <AppCheckbox
                checked={iconChecked}
                onChange={(e) => setIconChecked(e.target.checked)}
                label="Important setting"
                icon={FaInfo}
              />
            </div>
          </div>
        </div>

        {/* Design Features */}
        <div className="rounded-xl border border-stroke-neutral-decorative bg-back-neutral-secondary p-6">
          <H3 className="mb-4">Design Features</H3>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Body2 color="primary" className="font-semibold mb-2">
                ✅ Features Implemented:
              </Body2>
              <ul className="space-y-1">
                <Body2 color="secondary">• Hidden native checkbox for accessibility</Body2>
                <Body2 color="secondary">• Custom visual design with design tokens</Body2>
                <Body2 color="secondary">• Proper focus states and keyboard navigation</Body2>
                <Body2 color="secondary">• Hover effects and transitions</Body2>
                <Body2 color="secondary">• Error states with proper styling</Body2>
                <Body2 color="secondary">• Disabled states</Body2>
                <Body2 color="secondary">• App-typography components for text</Body2>
                <Body2 color="secondary">• Tailwind design tokens throughout</Body2>
              </ul>
            </div>
            <div>
              <Body2 color="primary" className="font-semibold mb-2">
                🎨 Design System Integration:
              </Body2>
              <ul className="space-y-1">
                <Body2 color="secondary">• Uses accent-primary for checked state</Body2>
                <Body2 color="secondary">• Uses stroke-neutral-decorative for borders</Body2>
                <Body2 color="secondary">• Uses fore-on-accent-primary for checkmark</Body2>
                <Body2 color="secondary">• Uses status-error for error states</Body2>
                <Body2 color="secondary">• Consistent with app-input styling patterns</Body2>
                <Body2 color="secondary">• Follows DRY principles</Body2>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

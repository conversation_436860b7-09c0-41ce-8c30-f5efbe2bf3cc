"use client";

import { useEffect } from "react";
import { usePathname } from "next/navigation";

/**
 * Hook to reset scroll position to top when navigating between pages
 * This prevents the odd scroll behavior where changing pages maintains the previous scroll position
 */
export function useScrollReset() {
  const pathname = usePathname();

  useEffect(() => {
    // Find the main scrollable container in the dashboard layout
    const scrollContainer = document.querySelector('.dashboard-scroll-container');
    
    if (scrollContainer) {
      // Reset scroll position to top
      scrollContainer.scrollTo(0, 0);
    } else {
      // Fallback to window scroll reset for non-dashboard pages
      window.scrollTo(0, 0);
    }
  }, [pathname]);
}

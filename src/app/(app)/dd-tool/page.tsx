"use client";

import axios from "axios";
import Link from "next/link";
import { useMemo, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { AppButton } from "../../components/app-button";
import { AppCode } from "../../components/app-code";
import { AppInput } from "../../components/app-input";
import { AppLogo } from "../../components/app-logo";
import { AppPageTitle } from "../../components/app-page-title";
import Footer from "../../components/Footer/Footer";

export type GitHubLinkFormValues = {
  // UX
  targetAddress: string;
  calldata: string;
  name: string;
};
export default function GovernanceFuzzingDemo() {
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<GitHubLinkFormValues>({
    defaultValues: {},
  });

  const targetAddress = watch(
    "targetAddress",
    "0x3Fa73f1E5d8A792C80F426fc8F84FBF7Ce9bBCAC"
  );
  const calldata = watch(
    "calldata",
    "function epochCount() external view returns (uint256)"
  );
  const name = watch("name", "Epoch Count");

  const dataForServer: any = useMemo(
    () => ({
      rpcUrl: "https://mainnet.infura.io/v3/********************************",
      sequence: [
        {
          call: {
            callInfo: {
              value: null,
              gasLimit: null,
              from: `${targetAddress}`,
            },
            contract: {
              functionString: `${calldata}`,
              address: `${targetAddress}`,
              inputs: [],
            },
          },
          inputMappings: [],
          outputMappings: [name],
        },
      ],
    }),
    [targetAddress, calldata, name]
  );

  const [result, setResult] = useState({});
  const [loading, setLoading] = useState(false);

  // TODO: Add to our API so we obfuscate
  async function runTestAndShowResult(e) {
    e.preventDefault();

    setLoading(true);
    try {
      const res = await axios({
        method: "POST",
        url: "https://dd-tool-server-a1023eb23069.herokuapp.com/sequence",
        data: dataForServer,
      });

      setResult(res.data);
    } catch (e) {
      //
    }
    setLoading(false);
  }

  // Test the event demo
  // For event we need:

  // ABI of event
  // Address to watch

  // For variables, we need the mapping of vars

  // To test, we also need a TX Hash of a previous one, we could also go and fetch them for the user

  // Click the button
  // Call the server with the data
  // Retrieve it
  // Show the VARS and the "Concrete Values" from the vars
  // In setting them the user is setting the variables, not the concrete values

  // console.log("result", result)

  // Event transformer
  // Call Transformer
  // Final Result Transformer

  // Dynamic Replacement
  // With a list of variables

  // eventName_var | either is a index or a name | but since a event is a call to the event, then we always have it
  // so we need to make sure the event identifier is valid, so prob must be the address + event
  // event + data + ETC
  // from it, generate the variables, the variables are the artifact index if we don't have names
  // or the names if we have all of them
  // We can add some return from the API

  // validateEvent -> Passes the minimum data necessary and return labels
  // labelEvent -> TO FIGURE OUT, but basicaly same as above

  // parseEvent.labels[] -> 0,1,2 or w/e the user set

  // This is the key
  // A way to make the call
  // A specification to grab the values I need

  // eventName_label -> Concrete input
  // Call 1 -> outputs are either named variables or indexed
  // so it would be call_INDEX_VAR

  // Each call is done subsequently (as we assume we're only calling view functions)
  // event_INDEX_VAR (event_0_varIndex)
  // call_INDEX_VAR (call_0_varIndex)

  // prefix_index
  // ABI to Inputs call_X_( 0, 1, 2, 3, etc..)
  // prefix_inputLabel
  // ABI to Inputs with Renames (0,)

  // More "elegantly" event_eventLabel_varLabel
  // More "elegantly" call_callLabel_varLabel
  // But tbh we can always use unnamed params and be done
  // the UX Improvement is non-zero
  // but we can solve via colors I think (each var has a different color)

  return (
    <div className="min-h-screen bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <div className="gradient-dark-bg flex items-center justify-between bg-back-neutral-tertiary px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
      </div>
      <div className="p-[45px]">
        <AppPageTitle className="mb-[20px]">
          Governance Fuzzing Demo
        </AppPageTitle>
        <h3 className="mb-[16px] text-[16px] leading-[19px] text-fore-neutral-primary">
          This is simple demo of a call done via the DD Tool <br />
          Technically we can perform calls that change state as well <br />
          But I think it's best to limit to view calls for now
        </h3>
        <FormProvider
          {...({
            register,
            handleSubmit,
            setValue,
            setError,
            watch,
            errors,
            isSubmitting,
          } as any)}
        ></FormProvider>
        <form onSubmit={() => {}}>
          <h3 className="mb-[22px] text-[28px] leading-[33px] text-fore-neutral-primary"></h3>

          <div className="flex flex-wrap gap-[40px]">
            <div className="border-r-divider min-w-[450px] border border-y-0 border-l-0 pr-[40px]">
              <div className="min-w-[400px]">
                <AppInput
                  className="mb-[8px]"
                  label="Target Address"
                  {...register("targetAddress")}
                  type="text"
                  defaultValue="0x3Fa73f1E5d8A792C80F426fc8F84FBF7Ce9bBCAC"
                />
                <AppInput
                  className="mb-[8px]"
                  label="Calldata"
                  {...register("calldata")}
                  type="text"
                  defaultValue="function epochCount() external view returns (uint256)"
                />
                <AppInput
                  className="mb-[8px]"
                  label="Label Name"
                  {...register("name")}
                  type="text"
                  defaultValue="Epoch Count"
                />
              </div>
              <AppButton onClick={runTestAndShowResult}>
                {loading ? "Loading" : "Run the tool"}
              </AppButton>
            </div>

            <AppCode language="json" code={JSON.stringify(result)} />
          </div>
        </form>
      </div>
      <Footer />
    </div>
  );
}

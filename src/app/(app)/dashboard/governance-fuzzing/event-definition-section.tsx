"use client";

import { useFormContext, useFieldArray } from "react-hook-form";
import { AppButton } from "@/app/components/app-button";
import { AppInput } from "@/app/components/app-input";
import { H2, Body3 } from "@/app/components/app-typography";
import { EventParameterItem } from "./event-parameter-item";
import { FORM_STYLES, type GitHubLinkFormValues } from "./constants";

interface EventDefinitionSectionProps {
  showEventDefinition: boolean;
  onShowEventDefinition: (show: boolean) => void;
  topic: string;
  eventDefinition: string;
}

export function EventDefinitionSection({
  showEventDefinition,
  onShowEventDefinition,
  topic,
  eventDefinition,
}: EventDefinitionSectionProps) {
  const { register } = useFormContext<GitHubLinkFormValues>();
  const { fields, append, remove } = useFieldArray({
    name: "parameters",
  });

  return (
    <div className="space-y-6">
      <div className={FORM_STYLES.divider} />

      {!showEventDefinition ? (
        <div className={FORM_STYLES.inputGroupSingle}>
          <div className={FORM_STYLES.fieldContainer}>
            <AppButton
              type="button"
              onClick={() => onShowEventDefinition(true)}
            >
              Define New Event
            </AppButton>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          <h3 className={FORM_STYLES.sectionTitle}>Event Definition</h3>

          <div className={FORM_STYLES.inputGroupSingle}>
            <div className={FORM_STYLES.fieldContainer}>
              <AppInput
                className={FORM_STYLES.input}
                label="Event Name"
                placeholder="e.g. AddNumber"
                {...register("eventName", { required: true })}
                type="text"
              />
            </div>
          </div>

          <div className="space-y-4">
            <H2 className="mb-[16px]" color="primary">
              Event Parameters ( MUST match event definition )
            </H2>

            {fields.map((field, index) => (
              <EventParameterItem
                key={field.id}
                index={index}
                fieldId={field.id}
                onRemove={remove}
              />
            ))}

            <AppButton
              type="button"
              onClick={() =>
                append({
                  type: "",
                  isIndexed: false,
                  replacement: "",
                  unused: false,
                })
              }
            >
              Add Parameter
            </AppButton>
          </div>
        </div>
      )}

      {topic && eventDefinition && (
        <div className="space-y-4">
          <div className={FORM_STYLES.divider} />

          <div className="space-y-2">
            <h3 className={FORM_STYLES.sectionTitle}>
              Generated Event Information
            </h3>
            <div className="space-y-2 rounded-lg bg-back-neutral-tertiary p-4">
              <Body3 color="primary">
                <strong>Topic:</strong> {topic}
              </Body3>
              <Body3 color="primary">
                <strong>Event Definition:</strong> {eventDefinition}
              </Body3>
            </div>
            <Body3 color="secondary" className="text-sm">
              Make sure you verify this topic is correct before submitting.
              Incorrect topic will make us miss events on chain.
            </Body3>
          </div>
        </div>
      )}

      {showEventDefinition && (
        <div className="mt-8">
          <AppButton type="submit">Submit Governance Fuzzing Setup</AppButton>
        </div>
      )}
    </div>
  );
}

"use client";

import type { ReactNode } from "react";
import { useScrollReset } from "@/app/hooks/useScrollReset";

interface DashboardScrollContainerProps {
  children: ReactNode;
}

/**
 * Wrapper component for the main dashboard content area that handles scroll reset on navigation
 */
export function DashboardScrollContainer({
  children,
}: DashboardScrollContainerProps) {
  // Reset scroll position when navigating between pages
  useScrollReset();

  return (
    <div className="dashboard-scroll-container flex grow flex-col overflow-y-auto bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <div className="grow">{children}</div>
    </div>
  );
}

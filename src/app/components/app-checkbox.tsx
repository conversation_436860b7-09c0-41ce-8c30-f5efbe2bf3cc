import { forwardRef, useId } from "react";
import type { IconType } from "react-icons/lib";
import { IoCheckmark } from "react-icons/io5";

import { cn } from "../helpers/cn";
import { Body2 } from "./app-typography";

type AppCheckboxProps = {
  className?: string;
  defaultChecked?: boolean;
  disabled?: boolean;
  onChange?: (e: any) => void;
  checked?: boolean;
  label?: string;
  containerClassName?: string;
  error?: string;
  disableError?: boolean;
  icon?: IconType;
  tooltip?: string; // Tooltip text
};

export const AppCheckbox = forwardRef(
  (
    {
      onChange,
      defaultChecked,
      disabled,
      checked,
      className = "",
      label,
      containerClassName = "",
      error,
      icon: Icon,
      disableError = false,
      tooltip, // Tooltip text
      ...rest
    }: AppCheckboxProps,
    ref: any
  ) => {
    const id = useId();

    const getCustomCheckboxStyles = () => {
      const baseStyles =
        "relative h-4 w-4 rounded-sm border transition-all duration-200 cursor-pointer flex items-center justify-center";

      if (disabled) {
        return cn(
          baseStyles,
          "border-stroke-neutral-decorative bg-transparent opacity-50 cursor-not-allowed"
        );
      }

      if (error) {
        return cn(
          baseStyles,
          "border-status-error bg-transparent hover:border-status-error/80 focus-within:border-status-error focus-within:ring-2 focus-within:ring-status-error/20"
        );
      }

      if (checked) {
        return cn(
          baseStyles,
          "border-accent-primary bg-accent-primary hover:bg-accent-primary/90 focus-within:ring-2 focus-within:ring-accent-primary/20"
        );
      }

      return cn(
        baseStyles,
        "border-stroke-neutral-decorative bg-transparent hover:border-fore-neutral-primary focus-within:border-accent-primary focus-within:ring-2 focus-within:ring-accent-primary/20"
      );
    };

    return (
      <div
        className={cn("relative flex items-center gap-2", containerClassName)}
      >
        <div className="group relative">
          {/* Custom checkbox container with native input */}
          <label
            className={cn(getCustomCheckboxStyles(), className)}
            htmlFor={id}
          >
            {/* Hidden native checkbox for accessibility */}
            <input
              id={id}
              type="checkbox"
              className="absolute inset-0 opacity-0 cursor-pointer"
              {...{
                defaultChecked,
                checked,
                onChange,
                disabled,
                ref,
                ...rest,
              }}
            />

            {/* Custom checkbox visual */}
            {checked && (
              <IoCheckmark
                className="h-3 w-3 text-fore-on-accent-primary"
                strokeWidth={2}
              />
            )}
          </label>

          {tooltip && (
            <div className="absolute bottom-[130%] left-1/2 mb-2 hidden w-max max-w-xs -translate-x-1/2 rounded-md border border-stroke-neutral-decorative bg-back-neutral-primary px-3 py-1 text-sm shadow-lg group-hover:block">
              <Body2 color="secondary">{tooltip}</Body2>
            </div>
          )}
        </div>

        {label && (
          <label
            htmlFor={id}
            className={cn("cursor-pointer", {
              "opacity-50": disabled,
            })}
          >
            <Body2 color="secondary">{label}</Body2>
          </label>
        )}

        {!!Icon && <Icon className="size-4 text-fore-neutral-tertiary" />}

        {!disableError && error && (
          <Body2
            color="secondary"
            className="mt-[3px] block h-[14px] text-status-error"
          >
            {error}
          </Body2>
        )}
      </div>
    );
  }
);

AppCheckbox.displayName = "AppCheckbox";

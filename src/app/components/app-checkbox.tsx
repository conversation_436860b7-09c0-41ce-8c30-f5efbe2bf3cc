import { forwardRef, useId } from "react";
import type { IconType } from "react-icons/lib";

import { cn } from "../helpers/cn";
import { usePersistedTheme } from "../services/useThemePersistence";

type AppCheckboxProps = {
  className?: string;
  defaultChecked?: boolean;
  disabled?: boolean;
  onChange?: (e: any) => void;
  checked?: boolean;
  label?: string;
  containerClassName?: string;
  error?: string;
  disableError?: boolean;
  icon?: IconType;
  tooltip?: string; // Tooltip text
};

export const AppCheckbox = forwardRef(
  (
    {
      onChange,
      defaultChecked,
      disabled,
      checked,
      className = "",
      label,
      containerClassName = "",
      error,
      icon: Icon,
      disableError = false,
      tooltip, // Tooltip text
      ...rest
    }: AppCheckboxProps,
    ref: any
  ) => {
    const id = useId();
    const { isDark } = usePersistedTheme();

    const getCheckboxStyles = () => {
      const baseStyles =
        "h-4 w-4 rounded-sm outline-none transition-all duration-200 cursor-pointer";

      if (disabled) {
        return cn(
          baseStyles,
          isDark
            ? "border border-white/30 bg-transparent opacity-50 cursor-not-allowed"
            : "border border-black/30 bg-transparent opacity-50 cursor-not-allowed"
        );
      }

      if (error) {
        return cn(
          baseStyles,
          "border border-red-500 bg-transparent text-accent-primary hover:border-red-400 focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
        );
      }

      return cn(
        baseStyles,
        isDark
          ? "border border-white/60 bg-transparent text-accent-primary hover:border-white focus:border-accent-primary focus:ring-2 focus:ring-accent-primary/20"
          : "border border-black/60 bg-transparent text-accent-primary hover:border-black focus:border-accent-primary focus:ring-2 focus:ring-accent-primary/20"
      );
    };

    return (
      <div
        className={cn("relative flex items-center gap-2", containerClassName)}
      >
        <div className="group relative">
          <input
            id={id}
            type="checkbox"
            className={cn(getCheckboxStyles(), className)}
            {...{
              defaultChecked,
              checked,
              onChange,
              disabled,
              ref,
              ...rest,
            }}
          />
          {tooltip && (
            <div className="absolute bottom-[130%] left-1/2 mb-2 hidden w-max max-w-xs -translate-x-1/2 rounded-md border border-stroke-neutral-decorative bg-back-neutral-primary px-3 py-1 text-sm shadow-lg group-hover:block">
              <span className={cn(isDark ? "text-white/80" : "text-black/80")}>
                {tooltip}
              </span>
            </div>
          )}
        </div>
        {label && (
          <label
            htmlFor={id}
            className={cn(
              "text-[15px] leading-[18px] cursor-pointer",
              isDark ? "text-white/80" : "text-black/80",
              {
                "opacity-50": disabled,
              }
            )}
          >
            {label}
          </label>
        )}
        {!!Icon && (
          <Icon
            className={cn("size-4", isDark ? "text-white/60" : "text-black/60")}
          />
        )}
        {!disableError && error && (
          <span className="mt-[3px] block h-[14px] text-[12px] text-red-500">
            {error}
          </span>
        )}
      </div>
    );
  }
);

AppCheckbox.displayName = "AppCheckbox";
